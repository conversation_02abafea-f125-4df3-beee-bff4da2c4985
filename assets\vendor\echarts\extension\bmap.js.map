{"version": 3, "file": "bmap.js", "sources": ["../../extension/bmap/BMapCoordSys.js", "../../extension/bmap/BMapModel.js", "../../extension/bmap/BMapView.js", "../../extension/bmap/bmap.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n// @ts-nocheck\n/* global BMap */\nimport { util as zrUtil, graphic, matrix } from 'echarts';\nfunction BMapCoordSys(bmap, api) {\n  this._bmap = bmap;\n  this.dimensions = ['lng', 'lat'];\n  this._mapOffset = [0, 0];\n  this._api = api;\n  this._projection = new BMap.MercatorProjection();\n}\nBMapCoordSys.prototype.type = 'bmap';\nBMapCoordSys.prototype.dimensions = ['lng', 'lat'];\nBMapCoordSys.prototype.setZoom = function (zoom) {\n  this._zoom = zoom;\n};\nBMapCoordSys.prototype.setCenter = function (center) {\n  this._center = this._projection.lngLatToPoint(new BMap.Point(center[0], center[1]));\n};\nBMapCoordSys.prototype.setMapOffset = function (mapOffset) {\n  this._mapOffset = mapOffset;\n};\nBMapCoordSys.prototype.getBMap = function () {\n  return this._bmap;\n};\nBMapCoordSys.prototype.dataToPoint = function (data) {\n  var point = new BMap.Point(data[0], data[1]);\n  // TODO mercator projection is toooooooo slow\n  // let mercatorPoint = this._projection.lngLatToPoint(point);\n  // let width = this._api.getZr().getWidth();\n  // let height = this._api.getZr().getHeight();\n  // let divider = Math.pow(2, 18 - 10);\n  // return [\n  //     Math.round((mercatorPoint.x - this._center.x) / divider + width / 2),\n  //     Math.round((this._center.y - mercatorPoint.y) / divider + height / 2)\n  // ];\n  var px = this._bmap.pointToOverlayPixel(point);\n  var mapOffset = this._mapOffset;\n  return [px.x - mapOffset[0], px.y - mapOffset[1]];\n};\nBMapCoordSys.prototype.pointToData = function (pt) {\n  var mapOffset = this._mapOffset;\n  pt = this._bmap.overlayPixelToPoint({\n    x: pt[0] + mapOffset[0],\n    y: pt[1] + mapOffset[1]\n  });\n  return [pt.lng, pt.lat];\n};\nBMapCoordSys.prototype.getViewRect = function () {\n  var api = this._api;\n  return new graphic.BoundingRect(0, 0, api.getWidth(), api.getHeight());\n};\nBMapCoordSys.prototype.getRoamTransform = function () {\n  return matrix.create();\n};\nBMapCoordSys.prototype.prepareCustoms = function () {\n  var rect = this.getViewRect();\n  return {\n    coordSys: {\n      // The name exposed to user is always 'cartesian2d' but not 'grid'.\n      type: 'bmap',\n      x: rect.x,\n      y: rect.y,\n      width: rect.width,\n      height: rect.height\n    },\n    api: {\n      coord: zrUtil.bind(this.dataToPoint, this),\n      size: zrUtil.bind(dataToCoordSize, this)\n    }\n  };\n};\nBMapCoordSys.prototype.convertToPixel = function (ecModel, finder, value) {\n  // here we ignore finder as only one bmap component is allowed\n  return this.dataToPoint(value);\n};\nBMapCoordSys.prototype.convertFromPixel = function (ecModel, finder, value) {\n  return this.pointToData(value);\n};\nfunction dataToCoordSize(dataSize, dataItem) {\n  dataItem = dataItem || [0, 0];\n  return zrUtil.map([0, 1], function (dimIdx) {\n    var val = dataItem[dimIdx];\n    var halfSize = dataSize[dimIdx] / 2;\n    var p1 = [];\n    var p2 = [];\n    p1[dimIdx] = val - halfSize;\n    p2[dimIdx] = val + halfSize;\n    p1[1 - dimIdx] = p2[1 - dimIdx] = dataItem[1 - dimIdx];\n    return Math.abs(this.dataToPoint(p1)[dimIdx] - this.dataToPoint(p2)[dimIdx]);\n  }, this);\n}\nvar Overlay;\n// For deciding which dimensions to use when creating list data\nBMapCoordSys.dimensions = BMapCoordSys.prototype.dimensions;\nfunction createOverlayCtor() {\n  function Overlay(root) {\n    this._root = root;\n  }\n  Overlay.prototype = new BMap.Overlay();\n  /**\n   * 初始化\n   *\n   * @param {BMap.Map} map\n   * @override\n   */\n  Overlay.prototype.initialize = function (map) {\n    map.getPanes().labelPane.appendChild(this._root);\n    return this._root;\n  };\n  /**\n   * @override\n   */\n  Overlay.prototype.draw = function () {};\n  return Overlay;\n}\nBMapCoordSys.create = function (ecModel, api) {\n  var bmapCoordSys;\n  var root = api.getDom();\n  // TODO Dispose\n  ecModel.eachComponent('bmap', function (bmapModel) {\n    var painter = api.getZr().painter;\n    var viewportRoot = painter.getViewportRoot();\n    if (typeof BMap === 'undefined') {\n      throw new Error('BMap api is not loaded');\n    }\n    Overlay = Overlay || createOverlayCtor();\n    if (bmapCoordSys) {\n      throw new Error('Only one bmap component can exist');\n    }\n    var bmap;\n    if (!bmapModel.__bmap) {\n      // Not support IE8\n      var bmapRoot = root.querySelector('.ec-extension-bmap');\n      if (bmapRoot) {\n        // Reset viewport left and top, which will be changed\n        // in moving handler in BMapView\n        viewportRoot.style.left = '0px';\n        viewportRoot.style.top = '0px';\n        root.removeChild(bmapRoot);\n      }\n      bmapRoot = document.createElement('div');\n      bmapRoot.className = 'ec-extension-bmap';\n      // fix #13424\n      bmapRoot.style.cssText = 'position:absolute;width:100%;height:100%';\n      root.appendChild(bmapRoot);\n      // initializes bmap\n      var mapOptions = bmapModel.get('mapOptions');\n      if (mapOptions) {\n        mapOptions = zrUtil.clone(mapOptions);\n        // Not support `mapType`, use `bmap.setMapType(MapType)` instead.\n        delete mapOptions.mapType;\n      }\n      bmap = bmapModel.__bmap = new BMap.Map(bmapRoot, mapOptions);\n      var overlay = new Overlay(viewportRoot);\n      bmap.addOverlay(overlay);\n      // Override\n      painter.getViewportRootOffset = function () {\n        return {\n          offsetLeft: 0,\n          offsetTop: 0\n        };\n      };\n    }\n    bmap = bmapModel.__bmap;\n    // Set bmap options\n    // centerAndZoom before layout and render\n    var center = bmapModel.get('center');\n    var zoom = bmapModel.get('zoom');\n    if (center && zoom) {\n      var bmapCenter = bmap.getCenter();\n      var bmapZoom = bmap.getZoom();\n      var centerOrZoomChanged = bmapModel.centerOrZoomChanged([bmapCenter.lng, bmapCenter.lat], bmapZoom);\n      if (centerOrZoomChanged) {\n        var pt = new BMap.Point(center[0], center[1]);\n        bmap.centerAndZoom(pt, zoom);\n      }\n    }\n    bmapCoordSys = new BMapCoordSys(bmap, api);\n    bmapCoordSys.setMapOffset(bmapModel.__mapOffset || [0, 0]);\n    bmapCoordSys.setZoom(zoom);\n    bmapCoordSys.setCenter(center);\n    bmapModel.coordinateSystem = bmapCoordSys;\n  });\n  ecModel.eachSeries(function (seriesModel) {\n    if (seriesModel.get('coordinateSystem') === 'bmap') {\n      seriesModel.coordinateSystem = bmapCoordSys;\n    }\n  });\n  // return created coordinate systems\n  return bmapCoordSys && [bmapCoordSys];\n};\nexport default BMapCoordSys;", "\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n// @ts-nocheck\nimport * as echarts from 'echarts';\nfunction v2Equal(a, b) {\n  return a && b && a[0] === b[0] && a[1] === b[1];\n}\nexport default echarts.extendComponentModel({\n  type: 'bmap',\n  getBMap: function () {\n    // __bmap is injected when creating BMapCoordSys\n    return this.__bmap;\n  },\n  setCenterAndZoom: function (center, zoom) {\n    this.option.center = center;\n    this.option.zoom = zoom;\n  },\n  centerOrZoomChanged: function (center, zoom) {\n    var option = this.option;\n    return !(v2Equal(center, option.center) && zoom === option.zoom);\n  },\n  defaultOption: {\n    center: [104.114129, 37.550339],\n    zoom: 5,\n    // 2.0 https://lbsyun.baidu.com/custom/index.htm\n    mapStyle: {},\n    // 3.0 https://lbsyun.baidu.com/index.php?title=open/custom\n    mapStyleV2: {},\n    // See https://lbsyun.baidu.com/cms/jsapi/reference/jsapi_reference.html#a0b1\n    mapOptions: {},\n    roam: false\n  }\n});", "\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n// @ts-nocheck\nimport * as echarts from 'echarts';\nfunction isEmptyObject(obj) {\n  for (var key in obj) {\n    if (obj.hasOwnProperty(key)) {\n      return false;\n    }\n  }\n  return true;\n}\nexport default echarts.extendComponentView({\n  type: 'bmap',\n  render: function (bMapModel, ecModel, api) {\n    var rendering = true;\n    var bmap = bMapModel.getBMap();\n    var viewportRoot = api.getZr().painter.getViewportRoot();\n    var coordSys = bMapModel.coordinateSystem;\n    var moveHandler = function (type, target) {\n      if (rendering) {\n        return;\n      }\n      var offsetEl = viewportRoot.parentNode.parentNode.parentNode;\n      var mapOffset = [-parseInt(offsetEl.style.left, 10) || 0, -parseInt(offsetEl.style.top, 10) || 0];\n      // only update style when map offset changed\n      var viewportRootStyle = viewportRoot.style;\n      var offsetLeft = mapOffset[0] + 'px';\n      var offsetTop = mapOffset[1] + 'px';\n      if (viewportRootStyle.left !== offsetLeft) {\n        viewportRootStyle.left = offsetLeft;\n      }\n      if (viewportRootStyle.top !== offsetTop) {\n        viewportRootStyle.top = offsetTop;\n      }\n      coordSys.setMapOffset(mapOffset);\n      bMapModel.__mapOffset = mapOffset;\n      api.dispatchAction({\n        type: 'bmapRoam',\n        animation: {\n          duration: 0\n        }\n      });\n    };\n    function zoomEndHandler() {\n      if (rendering) {\n        return;\n      }\n      api.dispatchAction({\n        type: 'bmapRoam',\n        animation: {\n          duration: 0\n        }\n      });\n    }\n    bmap.removeEventListener('moving', this._oldMoveHandler);\n    bmap.removeEventListener('moveend', this._oldMoveHandler);\n    bmap.removeEventListener('zoomend', this._oldZoomEndHandler);\n    bmap.addEventListener('moving', moveHandler);\n    bmap.addEventListener('moveend', moveHandler);\n    bmap.addEventListener('zoomend', zoomEndHandler);\n    this._oldMoveHandler = moveHandler;\n    this._oldZoomEndHandler = zoomEndHandler;\n    var roam = bMapModel.get('roam');\n    if (roam && roam !== 'scale') {\n      bmap.enableDragging();\n    } else {\n      bmap.disableDragging();\n    }\n    if (roam && roam !== 'move') {\n      bmap.enableScrollWheelZoom();\n      bmap.enableDoubleClickZoom();\n      bmap.enablePinchToZoom();\n    } else {\n      bmap.disableScrollWheelZoom();\n      bmap.disableDoubleClickZoom();\n      bmap.disablePinchToZoom();\n    }\n    /* map 2.0 */\n    var originalStyle = bMapModel.__mapStyle;\n    var newMapStyle = bMapModel.get('mapStyle') || {};\n    // FIXME, Not use JSON methods\n    var mapStyleStr = JSON.stringify(newMapStyle);\n    if (JSON.stringify(originalStyle) !== mapStyleStr) {\n      // FIXME May have blank tile when dragging if setMapStyle\n      if (!isEmptyObject(newMapStyle)) {\n        bmap.setMapStyle(echarts.util.clone(newMapStyle));\n      }\n      bMapModel.__mapStyle = JSON.parse(mapStyleStr);\n    }\n    /* map 3.0 */\n    var originalStyle2 = bMapModel.__mapStyle2;\n    var newMapStyle2 = bMapModel.get('mapStyleV2') || {};\n    // FIXME, Not use JSON methods\n    var mapStyleStr2 = JSON.stringify(newMapStyle2);\n    if (JSON.stringify(originalStyle2) !== mapStyleStr2) {\n      // FIXME May have blank tile when dragging if setMapStyle\n      if (!isEmptyObject(newMapStyle2)) {\n        bmap.setMapStyleV2(echarts.util.clone(newMapStyle2));\n      }\n      bMapModel.__mapStyle2 = JSON.parse(mapStyleStr2);\n    }\n    rendering = false;\n  }\n});", "\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n// @ts-nocheck\n/**\n * BMap component extension\n */\nimport * as echarts from 'echarts';\nimport BMapCoordSys from './BMapCoordSys.js';\nimport './BMapModel.js';\nimport './BMapView.js';\necharts.registerCoordinateSystem('bmap', BMapCoordSys);\n// Action\necharts.registerAction({\n  type: 'bmapRoam',\n  event: 'bmapRoam',\n  update: 'updateLayout'\n}, function (payload, ecModel) {\n  ecModel.eachComponent('bmap', function (bMapModel) {\n    var bmap = bMapModel.getBMap();\n    var center = bmap.getCenter();\n    bMapModel.setCenterAndZoom([center.lng, center.lat], bmap.getZoom());\n  });\n});\nexport var version = '1.0.0';"], "names": ["graphic", "matrix", "zrUtil", "echarts.extendComponentModel", "echarts.extendComponentView", "echarts.util", "echarts.registerCoordinateSystem", "echarts.registerAction"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;EA8CA,SAAS,YAAY,CAAC,IAAI,EAAE,GAAG,EAAE;EACjC,EAAE,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;EACpB,EAAE,IAAI,CAAC,UAAU,GAAG,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;EACnC,EAAE,IAAI,CAAC,UAAU,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EAC3B,EAAE,IAAI,CAAC,IAAI,GAAG,GAAG,CAAC;EAClB,EAAE,IAAI,CAAC,WAAW,GAAG,IAAI,IAAI,CAAC,kBAAkB,EAAE,CAAC;EACnD,CAAC;EACD,YAAY,CAAC,SAAS,CAAC,IAAI,GAAG,MAAM,CAAC;EACrC,YAAY,CAAC,SAAS,CAAC,UAAU,GAAG,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;EACnD,YAAY,CAAC,SAAS,CAAC,OAAO,GAAG,UAAU,IAAI,EAAE;EACjD,EAAE,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;EACpB,CAAC,CAAC;EACF,YAAY,CAAC,SAAS,CAAC,SAAS,GAAG,UAAU,MAAM,EAAE;EACrD,EAAE,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACtF,CAAC,CAAC;EACF,YAAY,CAAC,SAAS,CAAC,YAAY,GAAG,UAAU,SAAS,EAAE;EAC3D,EAAE,IAAI,CAAC,UAAU,GAAG,SAAS,CAAC;EAC9B,CAAC,CAAC;EACF,YAAY,CAAC,SAAS,CAAC,OAAO,GAAG,YAAY;EAC7C,EAAE,OAAO,IAAI,CAAC,KAAK,CAAC;EACpB,CAAC,CAAC;EACF,YAAY,CAAC,SAAS,CAAC,WAAW,GAAG,UAAU,IAAI,EAAE;EACrD,EAAE,IAAI,KAAK,GAAG,IAAI,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;EAC/C;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA,EAAE,IAAI,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC,mBAAmB,CAAC,KAAK,CAAC,CAAC;EACjD,EAAE,IAAI,SAAS,GAAG,IAAI,CAAC,UAAU,CAAC;EAClC,EAAE,OAAO,CAAC,EAAE,CAAC,CAAC,GAAG,SAAS,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,GAAG,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;EACpD,CAAC,CAAC;EACF,YAAY,CAAC,SAAS,CAAC,WAAW,GAAG,UAAU,EAAE,EAAE;EACnD,EAAE,IAAI,SAAS,GAAG,IAAI,CAAC,UAAU,CAAC;EAClC,EAAE,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC,mBAAmB,CAAC;EACtC,IAAI,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,GAAG,SAAS,CAAC,CAAC,CAAC;EAC3B,IAAI,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,GAAG,SAAS,CAAC,CAAC,CAAC;EAC3B,GAAG,CAAC,CAAC;EACL,EAAE,OAAO,CAAC,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,GAAG,CAAC,CAAC;EAC1B,CAAC,CAAC;EACF,YAAY,CAAC,SAAS,CAAC,WAAW,GAAG,YAAY;EACjD,EAAE,IAAI,GAAG,GAAG,IAAI,CAAC,IAAI,CAAC;EACtB,EAAE,OAAO,IAAIA,eAAO,CAAC,YAAY,CAAC,CAAC,EAAE,CAAC,EAAE,GAAG,CAAC,QAAQ,EAAE,EAAE,GAAG,CAAC,SAAS,EAAE,CAAC,CAAC;EACzE,CAAC,CAAC;EACF,YAAY,CAAC,SAAS,CAAC,gBAAgB,GAAG,YAAY;EACtD,EAAE,OAAOC,cAAM,CAAC,MAAM,EAAE,CAAC;EACzB,CAAC,CAAC;EACF,YAAY,CAAC,SAAS,CAAC,cAAc,GAAG,YAAY;EACpD,EAAE,IAAI,IAAI,GAAG,IAAI,CAAC,WAAW,EAAE,CAAC;EAChC,EAAE,OAAO;EACT,IAAI,QAAQ,EAAE;EACd;EACA,MAAM,IAAI,EAAE,MAAM;EAClB,MAAM,CAAC,EAAE,IAAI,CAAC,CAAC;EACf,MAAM,CAAC,EAAE,IAAI,CAAC,CAAC;EACf,MAAM,KAAK,EAAE,IAAI,CAAC,KAAK;EACvB,MAAM,MAAM,EAAE,IAAI,CAAC,MAAM;EACzB,KAAK;EACL,IAAI,GAAG,EAAE;EACT,MAAM,KAAK,EAAEC,YAAM,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC;EAChD,MAAM,IAAI,EAAEA,YAAM,CAAC,IAAI,CAAC,eAAe,EAAE,IAAI,CAAC;EAC9C,KAAK;EACL,GAAG,CAAC;EACJ,CAAC,CAAC;EACF,YAAY,CAAC,SAAS,CAAC,cAAc,GAAG,UAAU,OAAO,EAAE,MAAM,EAAE,KAAK,EAAE;EAC1E;EACA,EAAE,OAAO,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;EACjC,CAAC,CAAC;EACF,YAAY,CAAC,SAAS,CAAC,gBAAgB,GAAG,UAAU,OAAO,EAAE,MAAM,EAAE,KAAK,EAAE;EAC5E,EAAE,OAAO,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;EACjC,CAAC,CAAC;EACF,SAAS,eAAe,CAAC,QAAQ,EAAE,QAAQ,EAAE;EAC7C,EAAE,QAAQ,GAAG,QAAQ,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EAChC,EAAE,OAAOA,YAAM,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,UAAU,MAAM,EAAE;EAC9C,IAAI,IAAI,GAAG,GAAG,QAAQ,CAAC,MAAM,CAAC,CAAC;EAC/B,IAAI,IAAI,QAAQ,GAAG,QAAQ,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;EACxC,IAAI,IAAI,EAAE,GAAG,EAAE,CAAC;EAChB,IAAI,IAAI,EAAE,GAAG,EAAE,CAAC;EAChB,IAAI,EAAE,CAAC,MAAM,CAAC,GAAG,GAAG,GAAG,QAAQ,CAAC;EAChC,IAAI,EAAE,CAAC,MAAM,CAAC,GAAG,GAAG,GAAG,QAAQ,CAAC;EAChC,IAAI,EAAE,CAAC,CAAC,GAAG,MAAM,CAAC,GAAG,EAAE,CAAC,CAAC,GAAG,MAAM,CAAC,GAAG,QAAQ,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC;EAC3D,IAAI,OAAO,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,GAAG,IAAI,CAAC,WAAW,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC;EACjF,GAAG,EAAE,IAAI,CAAC,CAAC;EACX,CAAC;EACD,IAAI,OAAO,CAAC;EACZ;EACA,YAAY,CAAC,UAAU,GAAG,YAAY,CAAC,SAAS,CAAC,UAAU,CAAC;EAC5D,SAAS,iBAAiB,GAAG;EAC7B,EAAE,SAAS,OAAO,CAAC,IAAI,EAAE;EACzB,IAAI,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;EACtB,GAAG;EACH,EAAE,OAAO,CAAC,SAAS,GAAG,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;EACzC;EACA;EACA;EACA;EACA;EACA;EACA,EAAE,OAAO,CAAC,SAAS,CAAC,UAAU,GAAG,UAAU,GAAG,EAAE;EAChD,IAAI,GAAG,CAAC,QAAQ,EAAE,CAAC,SAAS,CAAC,WAAW,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;EACrD,IAAI,OAAO,IAAI,CAAC,KAAK,CAAC;EACtB,GAAG,CAAC;EACJ;EACA;EACA;EACA,EAAE,OAAO,CAAC,SAAS,CAAC,IAAI,GAAG,YAAY,EAAE,CAAC;EAC1C,EAAE,OAAO,OAAO,CAAC;EACjB,CAAC;EACD,YAAY,CAAC,MAAM,GAAG,UAAU,OAAO,EAAE,GAAG,EAAE;EAC9C,EAAE,IAAI,YAAY,CAAC;EACnB,EAAE,IAAI,IAAI,GAAG,GAAG,CAAC,MAAM,EAAE,CAAC;EAC1B;EACA,EAAE,OAAO,CAAC,aAAa,CAAC,MAAM,EAAE,UAAU,SAAS,EAAE;EACrD,IAAI,IAAI,OAAO,GAAG,GAAG,CAAC,KAAK,EAAE,CAAC,OAAO,CAAC;EACtC,IAAI,IAAI,YAAY,GAAG,OAAO,CAAC,eAAe,EAAE,CAAC;EACjD,IAAI,IAAI,OAAO,IAAI,KAAK,WAAW,EAAE;EACrC,MAAM,MAAM,IAAI,KAAK,CAAC,wBAAwB,CAAC,CAAC;EAChD,KAAK;EACL,IAAI,OAAO,GAAG,OAAO,IAAI,iBAAiB,EAAE,CAAC;EAC7C,IAAI,IAAI,YAAY,EAAE;EACtB,MAAM,MAAM,IAAI,KAAK,CAAC,mCAAmC,CAAC,CAAC;EAC3D,KAAK;EACL,IAAI,IAAI,IAAI,CAAC;EACb,IAAI,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE;EAC3B;EACA,MAAM,IAAI,QAAQ,GAAG,IAAI,CAAC,aAAa,CAAC,oBAAoB,CAAC,CAAC;EAC9D,MAAM,IAAI,QAAQ,EAAE;EACpB;EACA;EACA,QAAQ,YAAY,CAAC,KAAK,CAAC,IAAI,GAAG,KAAK,CAAC;EACxC,QAAQ,YAAY,CAAC,KAAK,CAAC,GAAG,GAAG,KAAK,CAAC;EACvC,QAAQ,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;EACnC,OAAO;EACP,MAAM,QAAQ,GAAG,QAAQ,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;EAC/C,MAAM,QAAQ,CAAC,SAAS,GAAG,mBAAmB,CAAC;EAC/C;EACA,MAAM,QAAQ,CAAC,KAAK,CAAC,OAAO,GAAG,0CAA0C,CAAC;EAC1E,MAAM,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;EACjC;EACA,MAAM,IAAI,UAAU,GAAG,SAAS,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC;EACnD,MAAM,IAAI,UAAU,EAAE;EACtB,QAAQ,UAAU,GAAGA,YAAM,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC;EAC9C;EACA,QAAQ,OAAO,UAAU,CAAC,OAAO,CAAC;EAClC,OAAO;EACP,MAAM,IAAI,GAAG,SAAS,CAAC,MAAM,GAAG,IAAI,IAAI,CAAC,GAAG,CAAC,QAAQ,EAAE,UAAU,CAAC,CAAC;EACnE,MAAM,IAAI,OAAO,GAAG,IAAI,OAAO,CAAC,YAAY,CAAC,CAAC;EAC9C,MAAM,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC;EAC/B;EACA,MAAM,OAAO,CAAC,qBAAqB,GAAG,YAAY;EAClD,QAAQ,OAAO;EACf,UAAU,UAAU,EAAE,CAAC;EACvB,UAAU,SAAS,EAAE,CAAC;EACtB,SAAS,CAAC;EACV,OAAO,CAAC;EACR,KAAK;EACL,IAAI,IAAI,GAAG,SAAS,CAAC,MAAM,CAAC;EAC5B;EACA;EACA,IAAI,IAAI,MAAM,GAAG,SAAS,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;EACzC,IAAI,IAAI,IAAI,GAAG,SAAS,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;EACrC,IAAI,IAAI,MAAM,IAAI,IAAI,EAAE;EACxB,MAAM,IAAI,UAAU,GAAG,IAAI,CAAC,SAAS,EAAE,CAAC;EACxC,MAAM,IAAI,QAAQ,GAAG,IAAI,CAAC,OAAO,EAAE,CAAC;EACpC,MAAM,IAAI,mBAAmB,GAAG,SAAS,CAAC,mBAAmB,CAAC,CAAC,UAAU,CAAC,GAAG,EAAE,UAAU,CAAC,GAAG,CAAC,EAAE,QAAQ,CAAC,CAAC;EAC1G,MAAM,IAAI,mBAAmB,EAAE;EAC/B,QAAQ,IAAI,EAAE,GAAG,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;EACtD,QAAQ,IAAI,CAAC,aAAa,CAAC,EAAE,EAAE,IAAI,CAAC,CAAC;EACrC,OAAO;EACP,KAAK;EACL,IAAI,YAAY,GAAG,IAAI,YAAY,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC;EAC/C,IAAI,YAAY,CAAC,YAAY,CAAC,SAAS,CAAC,WAAW,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EAC/D,IAAI,YAAY,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;EAC/B,IAAI,YAAY,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;EACnC,IAAI,SAAS,CAAC,gBAAgB,GAAG,YAAY,CAAC;EAC9C,GAAG,CAAC,CAAC;EACL,EAAE,OAAO,CAAC,UAAU,CAAC,UAAU,WAAW,EAAE;EAC5C,IAAI,IAAI,WAAW,CAAC,GAAG,CAAC,kBAAkB,CAAC,KAAK,MAAM,EAAE;EACxD,MAAM,WAAW,CAAC,gBAAgB,GAAG,YAAY,CAAC;EAClD,KAAK;EACL,GAAG,CAAC,CAAC;EACL;EACA,EAAE,OAAO,YAAY,IAAI,CAAC,YAAY,CAAC,CAAC;EACxC,CAAC;;EC5LD,SAAS,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE;EACvB,EAAE,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;EAClD,CAAC;AACcC,8BAA4B,CAAC;EAC5C,EAAE,IAAI,EAAE,MAAM;EACd,EAAE,OAAO,EAAE,YAAY;EACvB;EACA,IAAI,OAAO,IAAI,CAAC,MAAM,CAAC;EACvB,GAAG;EACH,EAAE,gBAAgB,EAAE,UAAU,MAAM,EAAE,IAAI,EAAE;EAC5C,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,MAAM,CAAC;EAChC,IAAI,IAAI,CAAC,MAAM,CAAC,IAAI,GAAG,IAAI,CAAC;EAC5B,GAAG;EACH,EAAE,mBAAmB,EAAE,UAAU,MAAM,EAAE,IAAI,EAAE;EAC/C,IAAI,IAAI,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;EAC7B,IAAI,OAAO,EAAE,OAAO,CAAC,MAAM,EAAE,MAAM,CAAC,MAAM,CAAC,IAAI,IAAI,KAAK,MAAM,CAAC,IAAI,CAAC,CAAC;EACrE,GAAG;EACH,EAAE,aAAa,EAAE;EACjB,IAAI,MAAM,EAAE,CAAC,UAAU,EAAE,SAAS,CAAC;EACnC,IAAI,IAAI,EAAE,CAAC;EACX;EACA,IAAI,QAAQ,EAAE,EAAE;EAChB;EACA,IAAI,UAAU,EAAE,EAAE;EAClB;EACA,IAAI,UAAU,EAAE,EAAE;EAClB,IAAI,IAAI,EAAE,KAAK;EACf,GAAG;EACH,CAAC,CAAC;;EC5BF,SAAS,aAAa,CAAC,GAAG,EAAE;EAC5B,EAAE,KAAK,IAAI,GAAG,IAAI,GAAG,EAAE;EACvB,IAAI,IAAI,GAAG,CAAC,cAAc,CAAC,GAAG,CAAC,EAAE;EACjC,MAAM,OAAO,KAAK,CAAC;EACnB,KAAK;EACL,GAAG;EACH,EAAE,OAAO,IAAI,CAAC;EACd,CAAC;AACcC,6BAA2B,CAAC;EAC3C,EAAE,IAAI,EAAE,MAAM;EACd,EAAE,MAAM,EAAE,UAAU,SAAS,EAAE,OAAO,EAAE,GAAG,EAAE;EAC7C,IAAI,IAAI,SAAS,GAAG,IAAI,CAAC;EACzB,IAAI,IAAI,IAAI,GAAG,SAAS,CAAC,OAAO,EAAE,CAAC;EACnC,IAAI,IAAI,YAAY,GAAG,GAAG,CAAC,KAAK,EAAE,CAAC,OAAO,CAAC,eAAe,EAAE,CAAC;EAC7D,IAAI,IAAI,QAAQ,GAAG,SAAS,CAAC,gBAAgB,CAAC;EAC9C,IAAI,IAAI,WAAW,GAAG,UAAU,IAAI,EAAE,MAAM,EAAE;EAC9C,MAAM,IAAI,SAAS,EAAE;EACrB,QAAQ,OAAO;EACf,OAAO;EACP,MAAM,IAAI,QAAQ,GAAG,YAAY,CAAC,UAAU,CAAC,UAAU,CAAC,UAAU,CAAC;EACnE,MAAM,IAAI,SAAS,GAAG,CAAC,CAAC,QAAQ,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,QAAQ,CAAC,QAAQ,CAAC,KAAK,CAAC,GAAG,EAAE,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC;EACxG;EACA,MAAM,IAAI,iBAAiB,GAAG,YAAY,CAAC,KAAK,CAAC;EACjD,MAAM,IAAI,UAAU,GAAG,SAAS,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC;EAC3C,MAAM,IAAI,SAAS,GAAG,SAAS,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC;EAC1C,MAAM,IAAI,iBAAiB,CAAC,IAAI,KAAK,UAAU,EAAE;EACjD,QAAQ,iBAAiB,CAAC,IAAI,GAAG,UAAU,CAAC;EAC5C,OAAO;EACP,MAAM,IAAI,iBAAiB,CAAC,GAAG,KAAK,SAAS,EAAE;EAC/C,QAAQ,iBAAiB,CAAC,GAAG,GAAG,SAAS,CAAC;EAC1C,OAAO;EACP,MAAM,QAAQ,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC;EACvC,MAAM,SAAS,CAAC,WAAW,GAAG,SAAS,CAAC;EACxC,MAAM,GAAG,CAAC,cAAc,CAAC;EACzB,QAAQ,IAAI,EAAE,UAAU;EACxB,QAAQ,SAAS,EAAE;EACnB,UAAU,QAAQ,EAAE,CAAC;EACrB,SAAS;EACT,OAAO,CAAC,CAAC;EACT,KAAK,CAAC;EACN,IAAI,SAAS,cAAc,GAAG;EAC9B,MAAM,IAAI,SAAS,EAAE;EACrB,QAAQ,OAAO;EACf,OAAO;EACP,MAAM,GAAG,CAAC,cAAc,CAAC;EACzB,QAAQ,IAAI,EAAE,UAAU;EACxB,QAAQ,SAAS,EAAE;EACnB,UAAU,QAAQ,EAAE,CAAC;EACrB,SAAS;EACT,OAAO,CAAC,CAAC;EACT,KAAK;EACL,IAAI,IAAI,CAAC,mBAAmB,CAAC,QAAQ,EAAE,IAAI,CAAC,eAAe,CAAC,CAAC;EAC7D,IAAI,IAAI,CAAC,mBAAmB,CAAC,SAAS,EAAE,IAAI,CAAC,eAAe,CAAC,CAAC;EAC9D,IAAI,IAAI,CAAC,mBAAmB,CAAC,SAAS,EAAE,IAAI,CAAC,kBAAkB,CAAC,CAAC;EACjE,IAAI,IAAI,CAAC,gBAAgB,CAAC,QAAQ,EAAE,WAAW,CAAC,CAAC;EACjD,IAAI,IAAI,CAAC,gBAAgB,CAAC,SAAS,EAAE,WAAW,CAAC,CAAC;EAClD,IAAI,IAAI,CAAC,gBAAgB,CAAC,SAAS,EAAE,cAAc,CAAC,CAAC;EACrD,IAAI,IAAI,CAAC,eAAe,GAAG,WAAW,CAAC;EACvC,IAAI,IAAI,CAAC,kBAAkB,GAAG,cAAc,CAAC;EAC7C,IAAI,IAAI,IAAI,GAAG,SAAS,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;EACrC,IAAI,IAAI,IAAI,IAAI,IAAI,KAAK,OAAO,EAAE;EAClC,MAAM,IAAI,CAAC,cAAc,EAAE,CAAC;EAC5B,KAAK,MAAM;EACX,MAAM,IAAI,CAAC,eAAe,EAAE,CAAC;EAC7B,KAAK;EACL,IAAI,IAAI,IAAI,IAAI,IAAI,KAAK,MAAM,EAAE;EACjC,MAAM,IAAI,CAAC,qBAAqB,EAAE,CAAC;EACnC,MAAM,IAAI,CAAC,qBAAqB,EAAE,CAAC;EACnC,MAAM,IAAI,CAAC,iBAAiB,EAAE,CAAC;EAC/B,KAAK,MAAM;EACX,MAAM,IAAI,CAAC,sBAAsB,EAAE,CAAC;EACpC,MAAM,IAAI,CAAC,sBAAsB,EAAE,CAAC;EACpC,MAAM,IAAI,CAAC,kBAAkB,EAAE,CAAC;EAChC,KAAK;EACL;EACA,IAAI,IAAI,aAAa,GAAG,SAAS,CAAC,UAAU,CAAC;EAC7C,IAAI,IAAI,WAAW,GAAG,SAAS,CAAC,GAAG,CAAC,UAAU,CAAC,IAAI,EAAE,CAAC;EACtD;EACA,IAAI,IAAI,WAAW,GAAG,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,CAAC;EAClD,IAAI,IAAI,IAAI,CAAC,SAAS,CAAC,aAAa,CAAC,KAAK,WAAW,EAAE;EACvD;EACA,MAAM,IAAI,CAAC,aAAa,CAAC,WAAW,CAAC,EAAE;EACvC,QAAQ,IAAI,CAAC,WAAW,CAACC,YAAY,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC,CAAC;EAC1D,OAAO;EACP,MAAM,SAAS,CAAC,UAAU,GAAG,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC;EACrD,KAAK;EACL;EACA,IAAI,IAAI,cAAc,GAAG,SAAS,CAAC,WAAW,CAAC;EAC/C,IAAI,IAAI,YAAY,GAAG,SAAS,CAAC,GAAG,CAAC,YAAY,CAAC,IAAI,EAAE,CAAC;EACzD;EACA,IAAI,IAAI,YAAY,GAAG,IAAI,CAAC,SAAS,CAAC,YAAY,CAAC,CAAC;EACpD,IAAI,IAAI,IAAI,CAAC,SAAS,CAAC,cAAc,CAAC,KAAK,YAAY,EAAE;EACzD;EACA,MAAM,IAAI,CAAC,aAAa,CAAC,YAAY,CAAC,EAAE;EACxC,QAAQ,IAAI,CAAC,aAAa,CAACA,YAAY,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC,CAAC;EAC7D,OAAO;EACP,MAAM,SAAS,CAAC,WAAW,GAAG,IAAI,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC;EACvD,KAAK;EACL,IAAI,SAAS,GAAG,KAAK,CAAC;EACtB,GAAG;EACH,CAAC,CAAC;;AC9FFC,kCAAgC,CAAC,MAAM,EAAE,YAAY,CAAC,CAAC;EACvD;AACAC,wBAAsB,CAAC;EACvB,EAAE,IAAI,EAAE,UAAU;EAClB,EAAE,KAAK,EAAE,UAAU;EACnB,EAAE,MAAM,EAAE,cAAc;EACxB,CAAC,EAAE,UAAU,OAAO,EAAE,OAAO,EAAE;EAC/B,EAAE,OAAO,CAAC,aAAa,CAAC,MAAM,EAAE,UAAU,SAAS,EAAE;EACrD,IAAI,IAAI,IAAI,GAAG,SAAS,CAAC,OAAO,EAAE,CAAC;EACnC,IAAI,IAAI,MAAM,GAAG,IAAI,CAAC,SAAS,EAAE,CAAC;EAClC,IAAI,SAAS,CAAC,gBAAgB,CAAC,CAAC,MAAM,CAAC,GAAG,EAAE,MAAM,CAAC,GAAG,CAAC,EAAE,IAAI,CAAC,OAAO,EAAE,CAAC,CAAC;EACzE,GAAG,CAAC,CAAC;EACL,CAAC,CAAC,CAAC;AACO,MAAC,OAAO,GAAG;;;;;;"}