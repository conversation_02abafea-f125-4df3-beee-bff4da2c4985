import Element from './Element';
import UnknownElement from './UnknownElement';
import RenderedElement from './RenderedElement';
import PathElement from './PathElement';
import SVGElement from './SVGElement';
import RectElement from './RectElement';
import CircleElement from './CircleElement';
import EllipseElement from './EllipseElement';
import LineElement from './LineElement';
import PolylineElement from './PolylineElement';
import PolygonElement from './PolygonElement';
import PatternElement from './PatternElement';
import MarkerElement from './MarkerElement';
import DefsElement from './DefsElement';
import GradientElement from './GradientElement';
import LinearGradientElement from './LinearGradientElement';
import RadialGradientElement from './RadialGradientElement';
import StopElement from './StopElement';
import AnimateElement from './AnimateElement';
import AnimateColorElement from './AnimateColorElement';
import AnimateTransformElement from './AnimateTransformElement';
import FontElement from './FontElement';
import FontFaceElement from './FontFaceElement';
import MissingGlyphElement from './MissingGlyphElement';
import GlyphElement from './GlyphElement';
import TextElement from './TextElement';
import TSpanElement from './TSpanElement';
import TRefElement from './TRefElement';
import AElement from './AElement';
import TextPathElement from './TextPathElement';
import ImageElement from './ImageElement';
import GElement from './GElement';
import SymbolElement from './SymbolElement';
import StyleElement from './StyleElement';
import UseElement from './UseElement';
import MaskElement from './MaskElement';
import ClipPathElement from './ClipPathElement';
import FilterElement from './FilterElement';
import FeDropShadowElement from './FeDropShadowElement';
import FeMorphologyElement from './FeMorphologyElement';
import FeCompositeElement from './FeCompositeElement';
import FeColorMatrixElement from './FeColorMatrixElement';
import FeGaussianBlurElement from './FeGaussianBlurElement';
import TitleElement from './TitleElement';
import DescElement from './DescElement';
declare const elements: {
    svg: typeof SVGElement;
    rect: typeof RectElement;
    circle: typeof CircleElement;
    ellipse: typeof EllipseElement;
    line: typeof LineElement;
    polyline: typeof PolylineElement;
    polygon: typeof PolygonElement;
    path: typeof PathElement;
    pattern: typeof PatternElement;
    marker: typeof MarkerElement;
    defs: typeof DefsElement;
    linearGradient: typeof LinearGradientElement;
    radialGradient: typeof RadialGradientElement;
    stop: typeof StopElement;
    animate: typeof AnimateElement;
    animateColor: typeof AnimateColorElement;
    animateTransform: typeof AnimateTransformElement;
    font: typeof FontElement;
    'font-face': typeof FontFaceElement;
    'missing-glyph': typeof MissingGlyphElement;
    glyph: typeof GlyphElement;
    text: typeof TextElement;
    tspan: typeof TSpanElement;
    tref: typeof TRefElement;
    a: typeof AElement;
    textPath: typeof TextPathElement;
    image: typeof ImageElement;
    g: typeof GElement;
    symbol: typeof SymbolElement;
    style: typeof StyleElement;
    use: typeof UseElement;
    mask: typeof MaskElement;
    clipPath: typeof ClipPathElement;
    filter: typeof FilterElement;
    feDropShadow: typeof FeDropShadowElement;
    feMorphology: typeof FeMorphologyElement;
    feComposite: typeof FeCompositeElement;
    feColorMatrix: typeof FeColorMatrixElement;
    feGaussianBlur: typeof FeGaussianBlurElement;
    title: typeof TitleElement;
    desc: typeof DescElement;
};
export default elements;
declare type Elements = typeof elements;
export declare type AnyElement = Elements[keyof Elements];
export { Element, UnknownElement, RenderedElement, PathElement, SVGElement, RectElement, CircleElement, EllipseElement, LineElement, PolylineElement, PolygonElement, PatternElement, MarkerElement, DefsElement, GradientElement, LinearGradientElement, RadialGradientElement, StopElement, AnimateElement, AnimateColorElement, AnimateTransformElement, FontElement, FontFaceElement, MissingGlyphElement, GlyphElement, TextElement, TSpanElement, TRefElement, AElement, TextPathElement, ImageElement, GElement, SymbolElement, StyleElement, UseElement, MaskElement, ClipPathElement, FilterElement, FeDropShadowElement, FeMorphologyElement, FeCompositeElement, FeColorMatrixElement, FeGaussianBlurElement, TitleElement, DescElement };
//# sourceMappingURL=elements.d.ts.map