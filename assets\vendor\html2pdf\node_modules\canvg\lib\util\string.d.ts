/**
 * HTML-safe compress white-spaces.
 * @param str - String to compress.
 * @returns String.
 */
export declare function compressSpaces(str: string): string;
/**
 * HTML-safe left trim.
 * @param str - String to trim.
 * @returns String.
 */
export declare function trimLeft(str: string): string;
/**
 * HTML-safe right trim.
 * @param str - String to trim.
 * @returns String.
 */
export declare function trimRight(str: string): string;
/**
 * String to numbers array.
 * @param str - Numbers string.
 * @returns Numbers array.
 */
export declare function toNumbers(str: string): number[];
/**
 * Normalize attribute name.
 * @param name - Attribute name.
 * @returns Normalized attribute name.
 */
export declare function normalizeAttributeName(name: string): string;
/**
 * Parse external URL.
 * @param url - CSS url string.
 * @returns Parsed URL.
 */
export declare function parseExternalUrl(url: string): string;
/**
 * Transform floats to integers in rgb colors.
 * @param color - Color to normalize.
 * @returns Normalized color.
 */
export declare function normalizeColor(color: string): string;
//# sourceMappingURL=string.d.ts.map