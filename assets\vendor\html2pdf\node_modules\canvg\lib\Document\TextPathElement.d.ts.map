{"version": 3, "file": "TextPathElement.d.ts", "sourceRoot": "", "sources": ["../../src/Document/TextPathElement.ts"], "names": [], "mappings": "AAAA,OAAO,EACN,kBAAkB,EAClB,MAAM,UAAU,CAAC;AAWlB,OAAO,UAAU,EAAE,EAClB,WAAW,EACX,MAAM,eAAe,CAAC;AACvB,OAAO,QAAQ,MAAM,YAAY,CAAC;AAClC,OAAO,WAAW,MAAM,eAAe,CAAC;AACxC,OAAO,WAAW,MAAM,eAAe,CAAC;AAExC,MAAM,WAAW,MAAM;IACtB,CAAC,EAAE,MAAM,CAAC;IACV,CAAC,EAAE,MAAM,CAAC;CACV;AAED,MAAM,WAAW,YAAY;IAC5B,IAAI,EAAE,WAAW,CAAC;IAClB,MAAM,EAAE,MAAM,EAAE,CAAC;IACjB,KAAK,CAAC,EAAE,MAAM,CAAC;IACf,UAAU,EAAE,MAAM,CAAC;CACnB;AAED,UAAU,YAAa,SAAQ,MAAM;IACpC,QAAQ,EAAE,MAAM,CAAC;CACjB;AAQD,UAAU,UAAU;IAGnB,IAAI,EAAE,MAAM,CAAC;IACb,QAAQ,EAAE,MAAM,CAAC;IACjB,EAAE,EAAE,YAAY,CAAC;IACjB,EAAE,EAAE,YAAY,CAAC;CACjB;AAED,MAAM,CAAC,OAAO,OAAO,eAAgB,SAAQ,WAAW;IACvD,IAAI,SAAc;IAClB,SAAS,CAAC,SAAS,SAAK;IACxB,SAAS,CAAC,UAAU,SAAK;IACzB,SAAS,CAAC,UAAU,SAAM;IAC1B,SAAS,CAAC,SAAS,EAAE,UAAU,EAAE,CAAQ;IACzC,SAAS,CAAC,QAAQ,CAAC,IAAI,EAAE,MAAM,CAAC;IAChC,SAAS,CAAC,QAAQ,CAAC,SAAS,EAAE,YAAY,EAAE,CAAC;IAC7C,OAAO,CAAC,kBAAkB,CAAgB;IAC1C,OAAO,CAAC,gBAAgB,CAAoB;IAC5C,OAAO,CAAC,QAAQ,CAAC,aAAa,CAAsC;gBAGnE,QAAQ,EAAE,QAAQ,EAClB,IAAI,EAAE,WAAW,EACjB,gBAAgB,CAAC,EAAE,OAAO;IAU3B,OAAO;IAIP,IAAI,CAAC,GAAG,EAAE,kBAAkB;IAkG5B,cAAc,CAAC,GAAG,EAAE,kBAAkB;IAoEtC,SAAS,CAAC,kBAAkB,CAAC,GAAG,SAAI;IAIpC,SAAS,CAAC,oBAAoB,CAC7B,GAAG,EAAE,kBAAkB,EACvB,MAAM,EAAE,MAAM,EACd,aAAa,EAAE,MAAM,EACrB,aAAa,EAAE,MAAM,EACrB,YAAY,EAAE,MAAM,EACpB,WAAW,EAAE,MAAM,EACnB,EAAE,EAAE,MAAM,EACV,CAAC,EAAE,MAAM,EACT,KAAK,EAAE,MAAM;;;;;;;;IAuDd,SAAS,CAAC,WAAW,CACpB,GAAG,EAAE,kBAAkB,EACvB,IAAI,CAAC,EAAE,MAAM;IAqBd,SAAS,CAAC,WAAW,CAAC,GAAG,EAAE,kBAAkB;IA8H7C,SAAS,CAAC,aAAa,CAAC,IAAI,EAAE,WAAW;IA2FzC,SAAS,CAAC,KAAK,CACd,UAAU,EAAE,UAAU,EACtB,MAAM,EAAE,MAAM,EAAE;IAUjB,SAAS,CAAC,KAAK,CACd,UAAU,EAAE,UAAU,EACtB,MAAM,EAAE,MAAM,EAAE;IAYjB,SAAS,CAAC,KAAK,CACd,UAAU,EAAE,UAAU,EACtB,MAAM,EAAE,MAAM,EAAE;IAYjB,SAAS,CAAC,KAAK,CACd,UAAU,EAAE,UAAU,EACtB,MAAM,EAAE,MAAM,EAAE;IAYjB,SAAS,CAAC,KAAK,CACd,UAAU,EAAE,UAAU,EACtB,MAAM,EAAE,MAAM,EAAE;IAkBjB,SAAS,CAAC,KAAK,CACd,UAAU,EAAE,UAAU,EACtB,MAAM,EAAE,MAAM,EAAE;IAoBjB,SAAS,CAAC,KAAK,CACd,UAAU,EAAE,UAAU,EACtB,MAAM,EAAE,MAAM,EAAE;IAejB,SAAS,CAAC,KAAK,CACd,UAAU,EAAE,UAAU,EACtB,MAAM,EAAE,MAAM,EAAE;IAiBjB,SAAS,CAAC,KAAK,CACd,UAAU,EAAE,UAAU;IAgCvB,SAAS,CAAC,UAAU,CACnB,CAAC,EAAE,MAAM,EACT,CAAC,EAAE,MAAM,EACT,WAAW,EAAE,WAAW,EACxB,MAAM,EAAE,MAAM,EAAE;IAsJjB,SAAS,CAAC,cAAc,CACvB,IAAI,EAAE,MAAM,EACZ,GAAG,EAAE,MAAM,EACX,GAAG,EAAE,MAAM,EACX,GAAG,EAAE,MAAM,EACX,GAAG,EAAE,MAAM,EACX,KAAK,SAAM,EACX,KAAK,SAAM;IA2DZ,SAAS,CAAC,cAAc,CAAC,QAAQ,EAAE,MAAM;IAyHzC,SAAS,CAAC,aAAa,CACtB,EAAE,EAAE,MAAM,EACV,EAAE,EAAE,MAAM,EACV,EAAE,EAAE,MAAM,EACV,EAAE,EAAE,MAAM;IAQX,SAAS,CAAC,aAAa;IAevB,SAAS,CAAC,qBAAqB,CAC9B,GAAG,EAAE,MAAM,EACX,GAAG,EAAE,MAAM,EACX,GAAG,EAAE,MAAM,EACX,GAAG,EAAE,MAAM,EACX,GAAG,EAAE,MAAM,EACX,GAAG,EAAE,MAAM,EACX,GAAG,EAAE,MAAM,EACX,GAAG,EAAE,MAAM,EACX,GAAG,EAAE,MAAM,GACT,MAAM;IAUT,SAAS,CAAC,yBAAyB,CAClC,GAAG,EAAE,MAAM,EACX,GAAG,EAAE,MAAM,EACX,GAAG,EAAE,MAAM,EACX,GAAG,EAAE,MAAM,EACX,GAAG,EAAE,MAAM,EACX,GAAG,EAAE,MAAM,EACX,GAAG,EAAE,MAAM,GACT,MAAM;IAUT,SAAS,CAAC,uBAAuB,CAChC,EAAE,EAAE,MAAM,EACV,EAAE,EAAE,MAAM,EACV,EAAE,EAAE,MAAM,EACV,EAAE,EAAE,MAAM,EACV,KAAK,EAAE,MAAM,EACb,GAAG,EAAE,MAAM,GACT,MAAM;IAeT,SAAS,CAAC,qBAAqB,CAC9B,SAAS,EAAE,MAAM,EACjB,cAAc,EAAE,MAAM;IA0CvB,SAAS,CAAC,yBAAyB,CAClC,cAAc,EAAE,MAAM,EACtB,IAAI,CAAC,EAAE,MAAM,EACb,SAAS,CAAC,EAAE,MAAM;CAkBnB"}